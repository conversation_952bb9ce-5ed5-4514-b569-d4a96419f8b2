<template>
  <div class="min-h-screen bg-gray-100">
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
          <h1 class="text-2xl font-semibold text-gray-900">仪表盘</h1>
          <p class="text-gray-500">欢迎回来! {{ user?.name || 'User' }}</p>
        </div>

        <!-- Stats cards -->
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <!-- Projects Card -->
          <div class="bg-white overflow-hidden shadow-lg rounded-lg transition-all duration-300 hover:shadow-xl border-t-4 border-blue-500">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-3 shadow-md">
                  <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      活跃项目
                    </dt>
                    <dd class="flex items-baseline">
                      <div class="text-2xl font-semibold text-gray-900">
                        {{ projectStats.active }}
                      </div>
                      <div class="ml-2 flex items-baseline text-sm font-semibold" :class="projectStats.trend > 0 ? 'text-green-600' : 'text-red-600'">
                        <svg v-if="projectStats.trend > 0" class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <svg v-else class="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">
                          {{ projectStats.trend > 0 ? '增加' : '减少' }}
                        </span>
                        {{ Math.abs(projectStats.trend) }}%
                      </div>
                    </dd>
                    <dd class="mt-1">
                      <div class="flex">
                        <span class="text-xs text-gray-500">总计: {{ projectStats.active + projectStats.completed + projectStats.delayed }}</span>
                        <span class="ml-2 text-xs text-gray-500">已完成: {{ projectStats.completed }}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6 border-t border-gray-100">
              <div class="text-sm flex justify-between items-center">
                <router-link to="/projects" class="font-medium text-blue-600 hover:text-blue-700 flex items-center">
                  查看所有项目
                  <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
                <span class="text-xs text-gray-500">更新于 {{ formatTime(projectStats.lastUpdated) }}</span>
              </div>
            </div>
          </div>

          <!-- Suppliers Card -->
          <div class="bg-white overflow-hidden shadow-lg rounded-lg transition-all duration-300 hover:shadow-xl border-t-4 border-purple-500">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-3 shadow-md">
                  <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      供应商
                    </dt>
                    <dd class="flex items-baseline">
                      <div class="text-2xl font-semibold text-gray-900">
                        {{ supplierStats.total }}
                      </div>
                      <div class="ml-2 flex items-baseline text-sm font-semibold" :class="supplierStats.trend > 0 ? 'text-green-600' : 'text-red-600'">
                        <svg v-if="supplierStats.trend > 0" class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <svg v-else class="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">
                          {{ supplierStats.trend > 0 ? '增加' : '减少' }}
                        </span>
                        {{ Math.abs(supplierStats.trend) }}%
                      </div>
                    </dd>
                    <dd class="mt-1">
                      <div class="flex">
                        <span class="text-xs text-gray-500">活跃: {{ supplierStats.active }}</span>
                        <span class="ml-2 text-xs text-gray-500">新增: {{ supplierStats.new }}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6 border-t border-gray-100">
              <div class="text-sm flex justify-between items-center">
                <router-link to="/suppliers" class="font-medium text-purple-600 hover:text-purple-700 flex items-center">
                  管理供应商
                  <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
                <span class="text-xs text-gray-500">更新于 {{ formatTime(supplierStats.lastUpdated) }}</span>
              </div>
            </div>
          </div>

          <!-- Purchases Card -->
          <div class="bg-white overflow-hidden shadow-lg rounded-lg transition-all duration-300 hover:shadow-xl border-t-4 border-green-500">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-3 shadow-md">
                  <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      本月采购额
                    </dt>
                    <dd class="flex items-baseline">
                      <div class="text-2xl font-semibold text-gray-900">
                        ¥{{ formatCurrency(purchaseStats.monthly) }}
                      </div>
                      <div class="ml-2 flex items-baseline text-sm font-semibold" :class="purchaseStats.trend > 0 ? 'text-green-600' : 'text-red-600'">
                        <svg v-if="purchaseStats.trend > 0" class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <svg v-else class="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">
                          {{ purchaseStats.trend > 0 ? '增加' : '减少' }}
                        </span>
                        {{ Math.abs(purchaseStats.trend) }}%
                      </div>
                    </dd>
                    <dd class="mt-1">
                      <div class="flex">
                        <span class="text-xs text-gray-500">年度: ¥{{ formatCurrency(purchaseStats.yearly) }}</span>
                        <span class="ml-2 text-xs text-gray-500">待处理: {{ purchaseStats.pending }}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6 border-t border-gray-100">
              <div class="text-sm flex justify-between items-center">
                <router-link to="/purchases" class="font-medium text-green-600 hover:text-green-700 flex items-center">
                  查看采购记录
                  <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
                <span class="text-xs text-gray-500">更新于 {{ formatTime(purchaseStats.lastUpdated) }}</span>
              </div>
            </div>
          </div>

          <!-- Inventory Card -->
          <div class="bg-white overflow-hidden shadow-lg rounded-lg transition-all duration-300 hover:shadow-xl border-t-4 border-yellow-500">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-3 shadow-md">
                  <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      库存物品
                    </dt>
                    <dd class="flex items-baseline">
                      <div class="text-2xl font-semibold text-gray-900">
                        {{ inventoryStats.items }}
                      </div>
                      <div class="ml-2 flex items-baseline text-sm font-semibold" :class="inventoryStats.trend > 0 ? 'text-green-600' : 'text-red-600'">
                        <svg v-if="inventoryStats.trend > 0" class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <svg v-else class="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">
                          {{ inventoryStats.trend > 0 ? '增加' : '减少' }}
                        </span>
                        {{ Math.abs(inventoryStats.trend) }}%
                      </div>
                    </dd>
                    <dd class="mt-1">
                      <div class="flex">
                        <span class="text-xs text-gray-500">库存不足: {{ inventoryStats.lowStock }}</span>
                        <span class="ml-2 text-xs text-gray-500">总价值: ¥{{ formatCurrency(inventoryStats.totalValue) }}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6 border-t border-gray-100">
              <div class="text-sm flex justify-between items-center">
                <router-link to="/inventory" class="font-medium text-yellow-600 hover:text-yellow-700 flex items-center">
                  查看库存
                  <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
                <span class="text-xs text-gray-500">更新于 {{ formatTime(inventoryStats.lastUpdated) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent projects section -->
        <div class="mt-8">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <h2 class="text-xl leading-6 font-semibold text-gray-900">近期项目</h2>
              <span class="ml-3 px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {{ recentProjects.length }} 个项目
              </span>
            </div>
            <router-link to="/projects/create" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              创建项目
            </router-link>
          </div>
          <div class="mt-4 bg-white shadow-lg rounded-lg overflow-hidden border border-gray-100">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr class="bg-gray-50">
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">进度</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止日期</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(project, index) in recentProjects" :key="index" class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center shadow-sm" :class="getProjectBgClass(project.status)">
                          <span class="text-white font-medium text-sm">{{ getProjectInitials(project.name) }}</span>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">{{ project.name }}</div>
                          <div class="text-xs text-gray-500 mt-0.5">{{ project.code }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ project.client }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusClass(project.status)">
                        {{ project.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2 flex-grow max-w-[100px]">
                          <div class="h-2.5 rounded-full" :class="getProgressClass(project.progress)" :style="{ width: project.progress + '%' }"></div>
                        </div>
                        <span class="text-xs font-medium text-gray-700">{{ project.progress }}%</span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <svg class="h-4 w-4 text-gray-400 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm text-gray-700">{{ formatDate(project.deadline) }}</span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <router-link :to="`/projects/${project.code}`" class="text-blue-600 hover:text-blue-900 mr-3">查看</router-link>
                      <router-link :to="`/projects/${project.code}/edit`" class="text-indigo-600 hover:text-indigo-900">编辑</router-link>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200 flex justify-between items-center">
              <span class="text-sm text-gray-700">
                显示 <span class="font-medium">{{ Math.min(5, recentProjects.length) }}</span> 个项目，共 <span class="font-medium">{{ recentProjects.length }}</span> 个
              </span>
              <router-link to="/projects" class="text-sm font-medium text-blue-600 hover:text-blue-700 flex items-center">
                查看所有项目
                <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </router-link>
            </div>
          </div>
        </div>

        <!-- Inventory Status Section -->
        <div class="mt-8">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <h2 class="text-lg leading-6 font-medium text-gray-900">库存状态</h2>
              <span class="ml-3 px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {{ formatDate(new Date()) }}
              </span>
            </div>
            <router-link to="/inventory" class="font-medium text-yellow-600 hover:text-yellow-700 flex items-center">
              查看所有库存
              <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </router-link>
          </div>
          <div class="mt-4">
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <!-- Low Stock Alert Card -->
              <div class="bg-white overflow-hidden shadow-lg rounded-lg border-t-4 border-red-500 transition-all duration-300 hover:shadow-xl">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-3 shadow-md">
                      <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <h3 class="text-lg font-medium text-gray-900 truncate">库存不足</h3>
                      <div class="mt-1 flex items-baseline">
                        <div class="text-3xl font-semibold text-red-600">
                          {{ inventoryStats.lowStock }}
                        </div>
                        <div class="ml-2 text-sm font-medium text-gray-500">
                          项物品
                        </div>
                      </div>
                      <div class="mt-1">
                        <div class="flex justify-between items-center text-xs text-gray-500">
                          <span>需要采购</span>
                          <span class="font-medium text-red-600">立即处理</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 border-t border-gray-100">
                  <router-link to="/inventory/low-stock" class="text-sm font-medium text-red-600 hover:text-red-700 flex items-center">
                    查看库存不足物品
                    <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </router-link>
                </div>
              </div>
              
              <!-- Total Value Card -->
              <div class="bg-white overflow-hidden shadow-lg rounded-lg border-t-4 border-blue-500 transition-all duration-300 hover:shadow-xl">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-3 shadow-md">
                      <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <h3 class="text-lg font-medium text-gray-900 truncate">库存总值</h3>
                      <div class="mt-1 flex items-baseline">
                        <div class="text-3xl font-semibold text-blue-600">
                          ¥{{ formatCurrency(inventoryStats.totalValue) }}
                        </div>
                      </div>
                      <div class="mt-1">
                        <div class="flex justify-between items-center text-xs text-gray-500">
                          <span>共计 {{ inventoryStats.items }} 项物品</span>
                          <span :class="inventoryValueTrend > 0 ? 'text-green-600' : 'text-red-600'" class="font-medium flex items-center">
                            <svg v-if="inventoryValueTrend > 0" class="h-3 w-3 mr-0.5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                            <svg v-else class="h-3 w-3 mr-0.5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            {{ Math.abs(inventoryValueTrend) }}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 border-t border-gray-100">
                  <router-link to="/inventory/value" class="text-sm font-medium text-blue-600 hover:text-blue-700 flex items-center">
                    查看详细价值报表
                    <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </router-link>
                </div>
              </div>
              
              <!-- Warehouse Status Card -->
              <div class="bg-white overflow-hidden shadow-lg rounded-lg border-t-4 border-green-500 transition-all duration-300 hover:shadow-xl">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-3 shadow-md">
                      <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                      </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <h3 class="text-lg font-medium text-gray-900 truncate">仓库使用率</h3>
                      <div class="mt-1 flex items-baseline">
                        <div class="text-3xl font-semibold text-green-600">
                          78%
                        </div>
                        <div class="ml-2 text-sm font-medium text-gray-500">
                          (3/4年度)
                        </div>
                      </div>
                      <div class="mt-2">
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                          <div class="bg-green-600 h-2.5 rounded-full" style="width: 78%"></div>
                        </div>
                        <div class="flex justify-between mt-1 text-xs text-gray-500">
                          <span>0%</span>
                          <span>50%</span>
                          <span>100%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 border-t border-gray-100">
                  <router-link to="/inventory/warehouse" class="text-sm font-medium text-green-600 hover:text-green-700 flex items-center">
                    查看仓库详情
                    <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

       
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { dashboardService } from '../services/apiService'

export default {
  name: '仪表盘View',
  setup() {
    const authStore = useAuthStore()
    const user = computed(() => authStore.user)
    
    // Loading states
    const loading = ref(true)
    const statsLoading = ref(false)
    const projectsLoading = ref(false)
    const documentsLoading = ref(false)
    
    // Business stats - 使用真实API数据
    const projectStats = ref({
      active: 0,
      completed: 0,
      delayed: 0,
      trend: 0,
      lastUpdated: new Date()
    })
    
    const supplierStats = ref({
      total: 0,
      active: 0,
      new: 0,
      trend: 0,
      lastUpdated: new Date()
    })
    
    const purchaseStats = ref({
      monthly: 0,
      yearly: 0,
      pending: 0,
      trend: 0,
      lastUpdated: new Date()
    })
    
    const inventoryStats = ref({
      items: 0,
      lowStock: 0,
      totalValue: 0,
      trend: 0,
      lastUpdated: new Date()
    })
    
    const inventoryValueTrend = ref(0)
    
    // Recent data
    const recentProjects = ref([])
    const recentDocuments = ref([])
    
    // 加载仪表板统计数据
    const loadDashboardStats = async () => {
      try {
        statsLoading.value = true
        console.log('🔄 开始加载仪表板统计数据...')
        
        // 并行加载所有统计数据
        const [
          projectStatsData,
          supplierStatsData,
          purchaseStatsData,
          inventoryStatsData
        ] = await Promise.all([
          dashboardService.getProjectStats(),
          dashboardService.getSupplierStats(),
          dashboardService.getPurchaseStats(),
          dashboardService.getInventoryStats()
        ])
        
        // 更新统计数据
        projectStats.value = {
          ...projectStatsData,
          lastUpdated: new Date(projectStatsData.lastUpdated)
        }
        
        supplierStats.value = {
          ...supplierStatsData,
          lastUpdated: new Date(supplierStatsData.lastUpdated)
        }
        
        purchaseStats.value = {
          ...purchaseStatsData,
          lastUpdated: new Date(purchaseStatsData.lastUpdated)
        }
        
        inventoryStats.value = {
          ...inventoryStatsData,
          lastUpdated: new Date(inventoryStatsData.lastUpdated)
        }
        
        inventoryValueTrend.value = inventoryStatsData.trend || 0
        
        console.log('✅ 仪表板统计数据加载完成', {
          projects: projectStats.value,
          suppliers: supplierStats.value,
          purchases: purchaseStats.value,
          inventory: inventoryStats.value
        })
        
      } catch (error) {
        console.error('❌ 加载仪表板统计数据失败:', error)
        // 保持默认值，不显示错误给用户，因为这不是关键功能
      } finally {
        statsLoading.value = false
      }
    }
    
    // 加载最近项目
    const loadRecentProjects = async () => {
      try {
        projectsLoading.value = true
        console.log('🔄 开始加载最近项目...')
        
        const projects = await dashboardService.getRecentProjects(5)
        recentProjects.value = projects.map(project => ({
          ...project,
          deadline: project.deadline ? new Date(project.deadline).toISOString() : null
        }))
        
        console.log('✅ 最近项目加载完成', recentProjects.value)
        
      } catch (error) {
        console.error('❌ 加载最近项目失败:', error)
        // 使用默认的示例数据作为后备
        recentProjects.value = [
          {
            name: '办公楼装修工程',
            code: 'PRJ-2023-001',
            client: '上海未来科技有限公司',
            status: '进行中',
            progress: 65,
            deadline: '2023-06-30T00:00:00Z'
          },
          {
            name: '数据中心建设',
            code: 'PRJ-2023-002',
            client: '北京云创网络科技公司',
            status: '计划中',
            progress: 25,
            deadline: '2023-08-15T00:00:00Z'
          }
        ]
      } finally {
        projectsLoading.value = false
      }
    }
    
    // 加载最近文档
    const loadRecentDocuments = async () => {
      try {
        documentsLoading.value = true
        console.log('🔄 开始加载最近文档...')
        
        const documents = await dashboardService.getRecentDocuments(5)
        recentDocuments.value = documents.map(doc => ({
          ...doc,
          updatedAt: new Date(doc.updatedAt).toISOString()
        }))
        
        console.log('✅ 最近文档加载完成', recentDocuments.value)
        
      } catch (error) {
        console.error('❌ 加载最近文档失败:', error)
        // 使用默认的示例数据作为后备
        recentDocuments.value = [
          {
            title: 'Project Proposal - Q2 2023',
            category: 'Proposal',
            excerpt: 'Strategic initiative for expanding market reach in Q2 2023',
            updatedAt: '2023-01-15T14:32:00Z'
          },
          {
            title: 'Meeting Notes - Product Team',
            category: 'Notes',
            excerpt: 'Discussion about new feature roadmap and timeline',
            updatedAt: '2023-01-12T09:15:00Z'
          }
        ]
      } finally {
        documentsLoading.value = false
      }
    }
    
    // 初始化数据加载
    const initializeDashboard = async () => {
      try {
        loading.value = true
        console.log('🚀 初始化仪表板...')
        
        // 并行加载所有数据
        await Promise.all([
          loadDashboardStats(),
          loadRecentProjects(),
          loadRecentDocuments()
        ])
        
        console.log('✅ 仪表板初始化完成')
        
      } catch (error) {
        console.error('❌ 仪表板初始化失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const formatTrend = (trend) => {
      if (trend > 0) {
        return `+${trend}%`
      } else if (trend < 0) {
        return `${trend}%`
      } else {
        return '0%'
      }
    }
    
    const formatLastUpdated = (date) => {
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      const minutes = Math.floor(diff / (60 * 1000))
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)
      
      if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    }
    
    const getCategoryClass = (category) => {
      const classes = {
        'Proposal': 'bg-blue-100 text-blue-800',
        'Notes': 'bg-gray-100 text-gray-800',
        'Marketing': 'bg-purple-100 text-purple-800',
        'Finance': 'bg-green-100 text-green-800',
        'HR': 'bg-orange-100 text-orange-800',
        'Legal': 'bg-red-100 text-red-800',
        'Research': 'bg-yellow-100 text-yellow-800'
      }
      
      return classes[category] || 'bg-gray-100 text-gray-800'
    }
    
    const getStatusClass = (status) => {
      const classes = {
        '进行中': 'bg-green-100 text-green-800',
        'in_progress': 'bg-green-100 text-green-800',
        '计划中': 'bg-blue-100 text-blue-800',
        'planning': 'bg-blue-100 text-blue-800',
        '已完成': 'bg-gray-100 text-gray-800',
        'completed': 'bg-gray-100 text-gray-800',
        '已延期': 'bg-red-100 text-red-800',
        'delayed': 'bg-red-100 text-red-800',
        '已暂停': 'bg-yellow-100 text-yellow-800',
        'on_hold': 'bg-yellow-100 text-yellow-800'
      }
      
      return classes[status] || 'bg-gray-100 text-gray-800'
    }
    
    const getProgressClass = (progress) => {
      if (progress < 25) return 'bg-red-600';
      if (progress < 50) return 'bg-yellow-600';
      if (progress < 75) return 'bg-blue-600';
      return 'bg-green-600';
    }
    
    const getProjectBgClass = (status) => {
      const classes = {
        '进行中': 'bg-green-500',
        'in_progress': 'bg-green-500',
        '计划中': 'bg-blue-500',
        'planning': 'bg-blue-500',
        '已完成': 'bg-gray-500',
        'completed': 'bg-gray-500',
        '已延期': 'bg-red-500',
        'delayed': 'bg-red-500',
        '已暂停': 'bg-yellow-500',
        'on_hold': 'bg-yellow-500'
      }
      
      return classes[status] || 'bg-gray-500'
    }
    
    const getProjectInitials = (name) => {
      return name
        .split('')
        .filter(char => /[\u4e00-\u9fa5]/.test(char)) // Get Chinese characters
        .slice(0, 2)
        .join('')
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未设定'
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }).format(date)
    }
    
    const formatTime = (date) => {
      if (!date) return ''
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      const minutes = Math.floor(diff / (60 * 1000))
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)
      
      if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    }
    
    const formatCurrency = (value) => {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value)
    }
    
    // 刷新数据的方法
    const refreshData = async () => {
      console.log('🔄 手动刷新仪表板数据...')
      await initializeDashboard()
    }
    
    onMounted(() => {
      console.log('📊 仪表盘组件已挂载，开始初始化...')
      initializeDashboard()
    })
    
    return {
      user,
      loading,
      statsLoading,
      projectsLoading,
      documentsLoading,
      recentDocuments,
      recentProjects,
      projectStats,
      supplierStats,
      purchaseStats,
      inventoryStats,
      inventoryValueTrend,
      getCategoryClass,
      getStatusClass,
      getProgressClass,
      getProjectBgClass,
      getProjectInitials,
      formatDate,
      formatTime,
      formatCurrency,
      refreshData
    }
  }
}
</script>