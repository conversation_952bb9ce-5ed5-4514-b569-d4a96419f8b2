/**
 * API服务
 * 负责与后端API进行通信
 */
import axios from 'axios';

// 配置常量
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || '',
  timeout: 20000, // 请求超时时间20秒
  retryCount: 2, // 网络错误重试次数
  retryDelay: 1000, // 重试延迟时间(毫秒)
  cacheTime: 60000, // 缓存时间(毫秒) - 1分钟
};

// 请求缓存
const requestCache = new Map();

// 调试API请求的日志函数
const logApiRequest = (method, url, data = null) => {
  console.log(`🚀 API ${method.toUpperCase()} 请求: ${url}`, data ? { data } : '');
};

const logApiResponse = (method, url, response) => {
  console.log(`✅ API ${method.toUpperCase()} 响应: ${url}`, response);
};

const logApiError = (method, url, error) => {
  console.error(`❌ API ${method.toUpperCase()} 错误: ${url}`, error);
  console.error('详细错误信息:', {
    message: error.message,
    status: error.response?.status,
    data: error.response?.data
  });
};

/**
 * 通用响应处理函数 - 处理不同格式的响应数据
 * @param {Object|Array} response - API响应数据
 * @param {string} entityName - 实体名称(如'users', 'products'等)
 * @returns {Array} - 标准化后的数据数组
 */
const processResponse = (response, entityName) => {
  // 如果响应本身就是数组，直接返回
  if (Array.isArray(response)) {
    return response;
  }

  // 检查是否有指定实体名称的属性(如response.users)
  if (response?.[entityName] && Array.isArray(response[entityName])) {
    return response[entityName];
  }

  // 检查是否有通用的results属性
  if (response?.results && Array.isArray(response.results)) {
    return response.results;
  }

  // 检查是否有data属性
  if (response?.data && Array.isArray(response.data)) {
    return response.data;
  }

  // 如果找不到有效的数组数据，记录警告并返回空数组
  console.warn(`API ${entityName} 响应格式异常:`, response);
  return [];
};

/**
 * 通用分页响应处理函数 - 处理带分页的响应数据
 * @param {Object|Array} response - API响应数据
 * @param {string} entityName - 实体名称(如'users', 'products'等)
 * @param {Object} params - 请求参数
 * @returns {Object} - 标准化后的分页数据对象
 */
const processPaginatedResponse = (response, entityName, params = {}) => {
  const defaultPage = params.page || 1;
  const defaultLimit = params.limit || 10;

  // 处理数组响应
  if (Array.isArray(response)) {
    return {
      results: response,
      totalResults: response.length,
      page: defaultPage,
      limit: defaultLimit,
      totalPages: Math.ceil(response.length / defaultLimit) || 1
    };
  }

  // 处理包含指定实体名称属性的响应
  if (response?.[entityName] && Array.isArray(response[entityName])) {
    return {
      results: response[entityName],
      totalResults: response.totalCount || response[entityName].length,
      page: response.page || defaultPage,
      limit: response.limit || defaultLimit,
      totalPages: response.totalPages || Math.ceil(response[entityName].length / defaultLimit) || 1
    };
  }

  // 处理包含 results 属性的响应
  if (response?.results && Array.isArray(response.results)) {
    return {
      results: response.results,
      totalResults: response.pagination?.totalResults || response.totalCount || response.results.length,
      page: response.pagination?.page || response.page || defaultPage,
      limit: response.pagination?.limit || response.limit || defaultLimit,
      totalPages: response.pagination?.totalPages || response.totalPages ||
                 Math.ceil(response.results.length / defaultLimit) || 1
    };
  }

  // 处理意外的响应格式
  console.warn(`API ${entityName} 分页响应格式异常:`, response);
  return {
    results: [],
    totalResults: 0,
    page: defaultPage,
    limit: defaultLimit,
    totalPages: 0
  };
};

/**
 * 通用错误处理函数
 * @param {Error} error - 原始错误对象
 * @param {string} entityName - 实体名称
 * @param {string} operation - 操作类型(如'获取', '创建'等)
 * @returns {Error} - 包装后的错误对象
 */
const handleApiError = (error, entityName, operation) => {
  // 记录错误
  console.error(`${operation}${entityName}失败:`, error);

  // 特定错误类型处理
  if (error.message?.includes('Network Error')) {
    return new Error(`网络错误: 无法连接到服务器，请检查网络连接`, { cause: error });
  }

  if (error.code === 'ECONNABORTED') {
    return new Error(`请求超时: 服务器响应时间过长，请稍后重试`, { cause: error });
  }

  // HTTP状态码错误处理
  if (error.response) {
    const status = error.response.status;
    const message = error.response.data?.message || '';

    switch (status) {
      case 400:
        return new Error(`请求错误: ${message || '提供的数据无效'}`, { cause: error });
      case 401:
        return new Error(`未授权: 请登录后重试`, { cause: error });
      case 403:
        return new Error(`禁止访问: 您没有权限执行此操作`, { cause: error });
      case 404:
        return new Error(`资源不存在: 请求的${entityName}不存在`, { cause: error });
      case 409:
        return new Error(`冲突: ${message || `${entityName}数据冲突`}`, { cause: error });
      case 422:
        return new Error(`验证错误: ${message || '提供的数据无效'}`, { cause: error });
      default:
        if (status >= 500) {
          return new Error(`服务器错误: 请稍后重试`, { cause: error });
        }
    }
  }

  // 默认错误处理
  return new Error(`${operation}${entityName}时发生错误: ${error.message || '未知错误'}`, { cause: error });
};

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: API_CONFIG.timeout,
  noCache:true,
  withCredentials: true, // 允许跨域请求携带凭证
});

// 打印API配置信息
console.log('💻 API服务配置:', API_CONFIG);

/**
 * 生成请求缓存的键
 * @param {Object} config - 请求配置
 * @returns {string} - 缓存键
 */
const getCacheKey = (config) => {
  const { method, url, params, data } = config;
  return `${method}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`;
};

/**
 * 检查是否应该使用缓存
 * @param {Object} config - 请求配置
 * @returns {boolean} - 是否应该使用缓存
 */
const shouldUseCache = (config) => {
  // 只缓存GET请求
  return config.method.toLowerCase() === 'get' && !config.noCache;
};

/**
 * 实现重试逻辑
 * @param {Function} fn - 要重试的函数
 * @param {number} retries - 重试次数
 * @param {number} delay - 重试间隔(毫秒)
 * @returns {Promise} - Promise对象
 */
const retryRequest = async (fn, retries, delay) => {
  try {
    return await fn();
  } catch (error) {
    // 只对网络错误或超时错误进行重试
    if (
      retries > 0 &&
      (error.message?.includes('Network Error') || error.code === 'ECONNABORTED')
    ) {
      console.log(`请求失败，${retries}次重试机会剩余，等待${delay}ms后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return retryRequest(fn, retries - 1, delay);
    }
    throw error;
  }
};

// 请求拦截器 - 添加认证令牌和缓存处理
apiClient.interceptors.request.use(
  async (config) => {
    // 记录请求
    logApiRequest(config.method, config.url, config.data);

    // 添加认证令牌
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加XSRF-TOKEN如果存在
    const xsrfToken = document.cookie.match(/XSRF-TOKEN=([\w-]+)/);
    if (xsrfToken) {
      config.headers['X-XSRF-TOKEN'] = xsrfToken[1];
    }

    // 检查缓存
    if (shouldUseCache(config)) {
      const cacheKey = getCacheKey(config);
      const cachedResponse = requestCache.get(cacheKey);

      if (cachedResponse) {
        const { data, timestamp } = cachedResponse;
        const now = Date.now();

        // 检查缓存是否过期
        if (now - timestamp < API_CONFIG.cacheTime) {
          console.log(`使用缓存的响应: ${config.url}`);
          // 返回特殊标记，让响应拦截器知道这是缓存的响应
          config.adapter = () => {
            return Promise.resolve({
              data,
              status: 200,
              statusText: 'OK',
              headers: {},
              config,
              request: {},
              __fromCache: true
            });
          };
        } else {
          // 缓存过期，删除
          requestCache.delete(cacheKey);
        }
      }
    }

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理常见错误和缓存
apiClient.interceptors.response.use(
  (response) => {
    // 如果是缓存的响应，直接返回数据
    if (response.__fromCache) {
      return response.data;
    }

    // 记录响应
    logApiResponse(response.config.method, response.config.url, response.data);

    // 如果是GET请求且没有禁用缓存，则缓存响应
    if (shouldUseCache(response.config)) {
      const cacheKey = getCacheKey(response.config);
      requestCache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now()
      });
    }

    return response.data;
  },
  async (error) => {
    // 记录具体错误
    logApiError(
      error.config?.method || 'unknown',
      error.config?.url || 'unknown',
      error
    );

    // 如果是网络错误，尝试重试
    if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
      if (!error.config?._retryCount) {
        error.config._retryCount = 0;
      }

      if (error.config._retryCount < API_CONFIG.retryCount) {
        error.config._retryCount++;

        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay));

        console.log(`重试请求 (${error.config._retryCount}/${API_CONFIG.retryCount}):`, error.config.url);
        return apiClient(error.config);
      }
    }

    // 处理401错误 - 未授权
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('token');

      // 如果不在登录页面，则重定向到登录页面
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    // 处理403错误 - 禁止访问
    if (error.response?.status === 403) {
      console.error('没有权限访问此资源');
    }

    // 处理500错误 - 服务器错误
    if (error.response?.status === 500) {
      console.error('服务器错误:', error.response.data);
    }

    return Promise.reject(error);
  }
);

/**
 * 用户相关API
 */

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} - 用户数组
 */
const getUsers = async (params) => {
  try {
    const response = await apiClient.get('/api/users', { params });
    return processResponse(response, 'users');
  } catch (error) {
    console.error('获取用户列表失败:', error);
    // 发生错误时返回空数组，而不是抛出异常
    return [];
  }
};

/**
 * 获取用户列表(带分页)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页用户数据
 */
const getUsersPaginated = async (params) => {
  try {
    const response = await apiClient.get('/api/users', { params });
    return processPaginatedResponse(response, 'users', params);
  } catch (error) {
    throw handleApiError(error, '用户列表', '获取');
  }
};

/**
 * 获取单个用户
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} - 用户对象
 */
const getUserById = async (id) => {
  try {
    // 添加noCache: true参数，确保每次都从服务器获取最新数据
    return await apiClient.get(`/api/users/${id}`);
  } catch (error) {
    throw handleApiError(error, '用户', '获取');
  }
};

/**
 * 获取用户列表供下拉菜单使用
 * @param {string} search - 搜索关键词
 * @returns {Promise<Array>} - 用户数组
 */
const getUsersForDropdown = async (search = '') => {
  try {
    const response = await apiClient.get('/api/users/dropdown', { params: { search } });
    // 确保返回的是数组
    return Array.isArray(response) ? response : (response?.data || []);
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return [];
  }
};

/**
 * 创建用户
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 创建的用户对象
 */
const createUser = async (userData) => {
  try {
    return await apiClient.post('/api/users', userData);
  } catch (error) {
    throw handleApiError(error, '用户', '创建');
  }
};

/**
 * 更新用户
 * @param {string} id - 用户ID
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 更新后的用户对象
 */
const updateUser = async (id, userData) => {
  try {
    return await apiClient.patch(`/api/users/${id}`, userData);
  } catch (error) {
    // Enhanced error logging
    console.error('Update user error details:', {
      status: error.response?.status,
      data: error.response?.data,
      validationErrors: error.response?.data?.errors,
      message: error.response?.data?.message
    });
    throw handleApiError(error, '用户', '更新');
  }
};

/**
 * 批量更新用户
 * @param {Object} criteria - 查询条件
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} - 更新结果
 */
const updateBatchUsers = async (criteria, updateData) => {
  try {
    return await apiClient.post('/api/users/update/batch', { criteria, updateData });
  } catch (error) {
    throw handleApiError(error, '用户', '批量更新');
  }
};

/**
 * 删除用户
 * @param {string} id - 用户ID
 * @returns {Promise<Object>} - 删除结果
 */
const deleteUser = async (id) => {
  try {
    return await apiClient.delete(`/api/users/${id}`);
  } catch (error) {
    throw handleApiError(error, '用户', '删除');
  }
};

/**
 * 产品相关API
 */

/**
 * 获取产品列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} - 产品数组
 */
const getProducts = async (params) => {
  try {
    const response = await apiClient.get('/api/products', { params });
    return processResponse(response, 'products');
  } catch (error) {
    console.error('获取产品列表失败:', error);
    return [];
  }
};

/**
 * 获取产品列表(带分页)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页产品数据
 */
const getProductsPaginated = async (params) => {
  try {
    const response = await apiClient.get('/api/products', { params });
    return processPaginatedResponse(response, 'products', params);
  } catch (error) {
    throw handleApiError(error, '产品列表', '获取');
  }
};

// 准备项目查询参数
const prepareProjectParams = (params) => {
  // 验证参数
  const validParams = params && typeof params === 'object' ? params : {};

  // 创建基本参数对象
  const apiParams = {
    page: validParams.page ? Number(validParams.page) : 1,
    limit: validParams.limit ? Number(validParams.limit) : 10,
    sort: validParams.sort,
    status: validParams.status,
    search: validParams.search,
    manager: validParams.manager,
    include: Array.isArray(validParams.include) ? validParams.include.join(',') : validParams.include
  };

  // 验证项目 ID
  if (validParams.projectId) {
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (guidRegex.test(validParams.projectId)) {
      apiParams.projectId = validParams.projectId;
    } else {
      throw new Error('验证错误: projectId 必须是有效的 GUID 格式');
    }
  }

  // 移除未定义的属性
  return Object.fromEntries(
    Object.entries(apiParams).filter(([_, value]) => value !== undefined)
  );
};

// 处理项目响应数据
const processProjectsResponse = (response, apiParams) => {
  // 处理数组响应
  if (Array.isArray(response)) {
    return {
      results: response,
      totalResults: response.length,
      page: apiParams.page,
      limit: apiParams.limit,
      totalPages: Math.ceil(response.length / apiParams.limit) || 1
    };
  }

  // 处理包含 projects 属性的响应
  if (response?.projects && Array.isArray(response.projects)) {
    return {
      results: response.projects,
      totalResults: response.totalCount || response.projects.length,
      page: response.page || apiParams.page,
      limit: response.limit || apiParams.limit,
      totalPages: response.totalPages || Math.ceil(response.projects.length / apiParams.limit) || 1
    };
  }

  // 处理包含 results 属性的响应
  if (response?.results && Array.isArray(response.results)) {
    return {
      results: response.results,
      totalResults: response.pagination?.totalResults || response.results.length,
      page: response.pagination?.page || apiParams.page,
      limit: response.pagination?.limit || apiParams.limit,
      totalPages: response.pagination?.totalPages || Math.ceil(response.results.length / apiParams.limit) || 1
    };
  }

  // 处理意外的响应格式
  console.warn('API projects response format unexpected:', response);
  return {
    results: [],
    totalResults: 0,
    page: apiParams.page,
    limit: apiParams.limit,
    totalPages: 0
  };
};

// 获取公开项目列表
const getPublicProjects = async (params) => {
  try {
    // 准备查询参数
    const apiParams = prepareProjectParams(params);
    console.log('API getPublicProjects params:', apiParams);

    // 调用公开的项目端点
    const response = await apiClient.get('/api/projects/public', { params: apiParams });

    // 处理响应数据
    return processProjectsResponse(response, apiParams);
  } catch (error) {
    console.error('Error fetching public projects:', error);

    // 提供更具体的错误信息
    if (error.message.includes('projectId')) {
      throw new Error(`验证错误: "projectId" 必须是有效的 GUID`);
    } else if (error.response?.status === 400) {
      throw new Error(`验证错误: ${error.response.data?.message || '请求参数无效'}`);
    } else if (error.response?.status === 404) {
      throw new Error('资源未找到: 项目列表不可用');
    } else if (error.response?.status >= 500) {
      throw new Error('服务器错误: 请稍后再试');
    }
    throw error; // Re-throw if not a specific case we handle
  }
};

// 获取项目列表
const getProjects = async (params) => {
  try {
    // 准备查询参数
    const apiParams = prepareProjectParams(params);
    console.log('API getProjects params:', apiParams);

    // 调用项目端点
    const response = await apiClient.get('/api/projects', { params: apiParams });

    // 处理响应数据
    return processProjectsResponse(response, apiParams);
  } catch (error) {
    console.error('Error fetching projects:', error);

    // 提供更具体的错误信息
    if (error.message.includes('projectId')) {
      throw new Error(`验证错误: "projectId" 必须是有效的 GUID`);
    } else if (error.response?.status === 400) {
      throw new Error(`验证错误: ${error.response.data?.message || '请求参数无效'}`);
    } else if (error.response?.status === 401) {
      throw new Error('权限错误: 未授权访问');
    } else if (error.response?.status === 404) {
      throw new Error('资源未找到: 项目列表不可用');
    } else if (error.response?.status >= 500) {
      throw new Error('服务器错误: 请稍后再试');
    }
    throw error; // Re-throw if not a specific case we handle
  }
};

// 获取单个项目
const getProjectById = async (id) => {
  try {
    console.log(`正在获取项目详情，ID: ${id}`);
    return await apiClient.get(`/api/projects/${id}`);
  } catch (error) {
    console.error(`获取项目详情失败，ID: ${id}，原因:`, error);
    if (error.response && error.response.status === 404) {
      console.error('项目不存在或已被删除');
    }
    throw error;
  }
};

// 创建项目
const createProject = async (projectData) => {
  try {
    return await apiClient.post('/api/projects', projectData);
  } catch (error) {
    console.error('Error creating project:', error);
    throw error;
  }
};

// 更新项目
const updateProject = async (id, projectData) => {
  try {
    return await apiClient.patch(`/api/projects/${id}`, projectData);
  } catch (error) {
    console.error(`Error updating project ${id}:`, error);
    throw error;
  }
};

// 删除项目
const deleteProject = async (id) => {
  try {
    return await apiClient.delete(`/api/projects/${id}`);
  } catch (error) {
    console.error(`Error deleting project ${id}:`, error);
    throw error;
  }
};

/**
 * 获取单个产品
 * @param {string} id - 产品ID
 * @returns {Promise<Object>} - 产品对象
 */
const getProductById = async (id) => {
  try {
    return await apiClient.get(`/api/products/${id}`);
  } catch (error) {
    throw handleApiError(error, '产品', '获取');
  }
};

/**
 * 创建产品
 * @param {Object} productData - 产品数据
 * @returns {Promise<Object>} - 创建的产品对象
 */
const createProduct = async (productData) => {
  try {
    return await apiClient.post('/api/products', productData);
  } catch (error) {
    throw handleApiError(error, '产品', '创建');
  }
};

/**
 * 更新产品
 * @param {string} id - 产品ID
 * @param {Object} productData - 产品数据
 * @returns {Promise<Object>} - 更新后的产品对象
 */
const updateProduct = async (id, productData) => {
  try {
    return await apiClient.patch(`/api/products/${id}`, productData);
  } catch (error) {
    throw handleApiError(error, '产品', '更新');
  }
};

/**
 * 删除产品
 * @param {string} id - 产品ID
 * @returns {Promise<Object>} - 删除结果
 */
const deleteProduct = async (id) => {
  try {
    return await apiClient.delete(`/api/products/${id}`);
  } catch (error) {
    throw handleApiError(error, '产品', '删除');
  }
};

/**
 * 订单相关API
 */

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} - 订单数组
 */
const getOrders = async (params) => {
  try {
    const response = await apiClient.get('/api/orders', { params });
    return processResponse(response, 'orders');
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return [];
  }
};

/**
 * 获取订单列表(带分页)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页订单数据
 */
const getOrdersPaginated = async (params) => {
  try {
    const response = await apiClient.get('/api/orders', { params });
    return processPaginatedResponse(response, 'orders', params);
  } catch (error) {
    throw handleApiError(error, '订单列表', '获取');
  }
};

/**
 * 获取单个订单
 * @param {string} id - 订单ID
 * @returns {Promise<Object>} - 订单对象
 */
const getOrderById = async (id) => {
  try {
    return await apiClient.get(`/api/orders/${id}`);
  } catch (error) {
    throw handleApiError(error, '订单', '获取');
  }
};

/**
 * 创建订单
 * @param {Object} orderData - 订单数据
 * @returns {Promise<Object>} - 创建的订单对象
 */
const createOrder = async (orderData) => {
  try {
    return await apiClient.post('/api/orders', orderData);
  } catch (error) {
    throw handleApiError(error, '订单', '创建');
  }
};

/**
 * 更新订单
 * @param {string} id - 订单ID
 * @param {Object} orderData - 订单数据
 * @returns {Promise<Object>} - 更新后的订单对象
 */
const updateOrder = async (id, orderData) => {
  try {
    return await apiClient.patch(`/api/orders/${id}`, orderData);
  } catch (error) {
    throw handleApiError(error, '订单', '更新');
  }
};

/**
 * 删除订单
 * @param {string} id - 订单ID
 * @returns {Promise<Object>} - 删除结果
 */
const deleteOrder = async (id) => {
  try {
    return await apiClient.delete(`/api/orders/${id}`);
  } catch (error) {
    throw handleApiError(error, '订单', '删除');
  }
};

// 获取公开供应商列表
const getPublicSuppliers = async (params) => {
  try {
    console.log('API getPublicSuppliers params:', params);
    const response = await apiClient.get('/api/suppliers/public', { params });
    return processPaginatedResponse(response, 'suppliers', params);
  } catch (error) {
    console.error('Error fetching public suppliers:', error);
    return {
      results: [],
      totalResults: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      totalPages: 0
    };
  }
};

// 获取供应商列表
const getSuppliers = async (params) => {
  try {
    console.log('API getSuppliers params:', params);
    const response = await apiClient.get('/api/suppliers', { params });
    return processPaginatedResponse(response, 'suppliers', params);
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    throw handleApiError(error, '供应商列表', '获取');
  }
};

// 获取公开财务统计
const getPublicFinanceStats = async (params) => {
  try {
    console.log('API getPublicFinanceStats params:', params);
    return await apiClient.get('/api/finance/public/stats', { params });
  } catch (error) {
    console.error('Error fetching public finance stats:', error);
    return {
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      incomeByCategory: {},
      expenseByCategory: {},
      monthlyTrend: []
    };
  }
};

// 获取财务统计
const getFinanceStats = async (params) => {
  try {
    console.log('API getFinanceStats params:', params);
    const response = await apiClient.get('/api/finance/stats', { params });
    return response; // 直接返回响应数据，不需要再访问 .data
  } catch (error) {
    console.error('Error fetching finance stats:', error);
    throw handleApiError(error, '财务统计', '获取');
  }
};

// 获取客户列表
const getClients = async (params) => {
  try {
    console.log('API getClients params:', params);
    const response = await apiClient.get('/api/clients', { params });
    return processPaginatedResponse(response, 'clients', params);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return {
      results: [],
      totalResults: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      totalPages: 0
    };
  }
};

// 获取单个客户
const getClientById = async (id) => {
  try {
    console.log(`正在获取客户详情，ID: ${id}`);
    const response = await apiClient.get(`/api/clients/${id}`);

    // 直接返回response，因为响应拦截器已经将response.data提取出来了
    return response;
  } catch (error) {
    console.error(`获取客户详情失败，ID: ${id}，原因:`, error);
    throw handleApiError(error, '客户', '获取');
  }
};

// 创建客户
const createClient = async (clientData) => {
  try {
    return await apiClient.post('/api/clients', clientData);
  } catch (error) {
    console.error('Error creating client:', error);
    throw error;
  }
};

// 更新客户
const updateClient = async (id, clientData) => {
  try {
    return await apiClient.patch(`/api/clients/${id}`, clientData);
  } catch (error) {
    console.error(`Error updating client ${id}:`, error);
    throw error;
  }
};

// 删除客户
const deleteClient = async (id) => {
  try {
    return await apiClient.delete(`/api/clients/${id}`);
  } catch (error) {
    console.error(`Error deleting client ${id}:`, error);
    throw error;
  }
};

/**
 * 获取建设单位建议列表
 * @param {string} search
 * @returns {Promise<string[]>}
 */
const getConstructionUnits = async (search) => {
  try {
    const params = {};
    if (search) params.search = search;
    const response = await apiClient.get('/api/projects/construction-units', { params });
    return response.data;
  } catch (error) {
    throw handleApiError(error, '建设单位', '获取');
  }
};

/**
 * 员工相关API
 */

/**
 * 获取所有员工列表(包括合同工和临时工)
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页员工数据
 */
const getAllEmployees = async (params) => {
  try {
    const response = await apiClient.get('/api/employees', { params });
    return processPaginatedResponse(response, 'employees', params);
  } catch (error) {
    console.error('获取员工列表失败:', error);
    return {
      results: [],
      totalResults: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      totalPages: 0
    };
  }
};

/**
 * 获取合同工列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页合同工数据
 */
const getContractWorkers = async (params) => {
  try {
    const response = await apiClient.get('/api/employees/contract-workers', { params });
    return processPaginatedResponse(response, 'contractWorkers', params);
  } catch (error) {
    console.error('获取合同工列表失败:', error);
    return {
      results: [],
      totalResults: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      totalPages: 0
    };
  }
};

/**
 * 获取临时工列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 分页临时工数据
 */
const getTemporaryWorkers = async (params) => {
  try {
    const response = await apiClient.get('/api/employees/temporary-workers', { params });
    return processPaginatedResponse(response, 'temporaryWorkers', params);
  } catch (error) {
    console.error('获取临时工列表失败:', error);
    return {
      results: [],
      totalResults: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      totalPages: 0
    };
  }
};

/**
 * 删除合同工
 * @param {string} workerId - 合同工ID
 * @returns {Promise<Object>} - 删除结果
 */
const deleteContractWorker = async (workerId) => {
  try {
    const response = await apiClient.delete(`/api/employees/contract-workers/${workerId}`);
    return response.data;
  } catch (error) {
    console.error('删除合同工失败:', error);
    throw error;
  }
};

/**
 * 删除临时工
 * @param {string} workerId - 临时工ID
 * @returns {Promise<Object>} - 删除结果
 */
const deleteTemporaryWorker = async (workerId) => {
  try {
    const response = await apiClient.delete(`/api/employees/temporary-workers/${workerId}`);
    return response.data;
  } catch (error) {
    console.error('删除临时工失败:', error);
    throw error;
  }
};

// 认证相关API
export const authService = {
  login: async (email, password) => {
    try {
      return await apiClient.post('/api/auth/login', { email, password });
    } catch (error) {
      throw handleApiError(error, '登录', '执行');
    }
  },
  register: async (name, email, password) => {
    try {
      return await apiClient.post('/api/auth/register', { name, email, password });
    } catch (error) {
      throw handleApiError(error, '注册', '执行');
    }
  },
  forgotPassword: async (email) => {
    try {
      return await apiClient.post('/api/auth/forgot-password', { email });
    } catch (error) {
      throw handleApiError(error, '找回密码', '执行');
    }
  },
  resetPassword: async (token, password) => {
    try {
      return await apiClient.post('/api/auth/reset-password', { token, password });
    } catch (error) {
      throw handleApiError(error, '重置密码', '执行');
    }
  },
  verifyEmail: async (token) => {
    try {
      return await apiClient.post('/api/auth/verify-email', { token });
    } catch (error) {
      throw handleApiError(error, '验证邮箱', '执行');
    }
  },
  getCurrentUser: async () => {
    try {
      return await apiClient.get('/api/auth/me');
    } catch (error) {
      throw handleApiError(error, '获取当前用户', '执行');
    }
  }
};

// 用户相关API
export const userService = {
  getUsers: getUsers,
  getUser: getUserById,
  createUser: createUser,
  updateUser: updateUser,
  deleteUser: deleteUser
};

// 分类相关API
export const categoryService = {
  getCategories: async (params) => {
    try {
      const response = await apiClient.get('/api/categories', { params });
      return processResponse(response, 'categories');
    } catch (error) {
      throw handleApiError(error, '分类列表', '获取');
    }
  },
  getCategory: async (id) => {
    try {
      return await apiClient.get(`/api/categories/${id}`);
    } catch (error) {
      throw handleApiError(error, '分类', '获取');
    }
  },
  createCategory: async (categoryData) => {
    try {
      return await apiClient.post('/api/categories', categoryData);
    } catch (error) {
      throw handleApiError(error, '分类', '创建');
    }
  },
  updateCategory: async (id, categoryData) => {
    try {
      return await apiClient.patch(`/api/categories/${id}`, categoryData);
    } catch (error) {
      throw handleApiError(error, '分类', '更新');
    }
  },
  deleteCategory: async (id) => {
    try {
      return await apiClient.delete(`/api/categories/${id}`);
    } catch (error) {
      throw handleApiError(error, '分类', '删除');
    }
  }
};

// 文档相关API
export const documentService = {
  getDocuments: async (params) => {
    try {
      const response = await apiClient.get('/api/documents', { params });
      return processResponse(response, 'documents');
    } catch (error) {
      throw handleApiError(error, '文档列表', '获取');
    }
  },
  getDocument: async (id) => {
    try {
      return await apiClient.get(`/api/documents/${id}`);
    } catch (error) {
      throw handleApiError(error, '文档', '获取');
    }
  },
  createDocument: async (documentData) => {
    try {
      return await apiClient.post('/api/documents', documentData);
    } catch (error) {
      throw handleApiError(error, '文档', '创建');
    }
  },
  updateDocument: async (id, documentData) => {
    try {
      return await apiClient.patch(`/api/documents/${id}`, documentData);
    } catch (error) {
      throw handleApiError(error, '文档', '更新');
    }
  },
  deleteDocument: async (id) => {
    try {
      return await apiClient.delete(`/api/documents/${id}`);
    } catch (error) {
      throw handleApiError(error, '文档', '删除');
    }
  }
};

// 标签相关API
export const tagService = {
  getTags: async (params) => {
    try {
      const response = await apiClient.get('/api/tags', { params });
      return processResponse(response, 'tags');
    } catch (error) {
      throw handleApiError(error, '标签列表', '获取');
    }
  },
  getTag: async (id) => {
    try {
      return await apiClient.get(`/api/tags/${id}`);
    } catch (error) {
      throw handleApiError(error, '标签', '获取');
    }
  },
  createTag: async (tagData) => {
    try {
      return await apiClient.post('/api/tags', tagData);
    } catch (error) {
      throw handleApiError(error, '标签', '创建');
    }
  },
  updateTag: async (id, tagData) => {
    try {
      return await apiClient.patch(`/api/tags/${id}`, tagData);
    } catch (error) {
      throw handleApiError(error, '标签', '更新');
    }
  },
  deleteTag: async (id) => {
    try {
      return await apiClient.delete(`/api/tags/${id}`);
    } catch (error) {
      throw handleApiError(error, '标签', '删除');
    }
  }
};

// 评论相关API
export const commentService = {
  getComments: async (documentId, params) => {
    try {
      return await apiClient.get(`/api/comments?documentId=${documentId}`, { params });
    } catch (error) {
      throw handleApiError(error, '评论列表', '获取');
    }
  },
  createComment: async (commentData) => {
    try {
      return await apiClient.post('/api/comments', commentData);
    } catch (error) {
      throw handleApiError(error, '评论', '创建');
    }
  },
  updateComment: async (id, commentData) => {
    try {
      return await apiClient.patch(`/api/comments/${id}`, commentData);
    } catch (error) {
      throw handleApiError(error, '评论', '更新');
    }
  },
  deleteComment: async (id) => {
    try {
      return await apiClient.delete(`/api/comments/${id}`);
    } catch (error) {
      throw handleApiError(error, '评论', '删除');
    }
  }
};

// 附件相关API
export const attachmentService = {
  uploadAttachment: async (documentId, file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentId', documentId);
      return await apiClient.post('/api/attachments', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    } catch (error) {
      throw handleApiError(error, '附件', '上传');
    }
  },
  getAttachment: async (id) => {
    try {
      return await apiClient.get(`/api/attachments/${id}`);
    } catch (error) {
      throw handleApiError(error, '附件', '获取');
    }
  },
  downloadAttachment: async (id) => {
    try {
      return await apiClient.get(`/api/attachments/${id}/download`, { responseType: 'blob' });
    } catch (error) {
      throw handleApiError(error, '附件', '下载');
    }
  },
  deleteAttachment: async (id) => {
    try {
      return await apiClient.delete(`/api/attachments/${id}`);
    } catch (error) {
      throw handleApiError(error, '附件', '删除');
    }
  }
};

// 项目相关API
export const projectService = {
  getProjects: getProjects,
  getPublicProjects: getPublicProjects,
  getProject: getProjectById,
  createProject: createProject,
  updateProject: updateProject,
  deleteProject: deleteProject
};

// 供应商相关API
export const supplierService = {
  getSuppliers: getSuppliers,
  getPublicSuppliers: getPublicSuppliers,
  getSupplier: async (id) => {
    try {
      return await apiClient.get(`/api/suppliers/${id}`);
    } catch (error) {
      throw handleApiError(error, '供应商', '获取');
    }
  },
  createSupplier: async (supplierData) => {
    try {
      return await apiClient.post('/api/suppliers', supplierData);
    } catch (error) {
      throw handleApiError(error, '供应商', '创建');
    }
  },
  updateSupplier: async (id, supplierData) => {
    try {
      return await apiClient.patch(`/api/suppliers/${id}`, supplierData);
    } catch (error) {
      throw handleApiError(error, '供应商', '更新');
    }
  },
  deleteSupplier: async (id) => {
    try {
      return await apiClient.delete(`/api/suppliers/${id}`);
    } catch (error) {
      throw handleApiError(error, '供应商', '删除');
    }
  }
};

// 采购相关API
export const purchaseService = {
  getPurchases: async (params) => {
    try {
      const response = await apiClient.get('/api/purchases', { params });
      return processResponse(response, 'purchases');
    } catch (error) {
      throw handleApiError(error, '采购列表', '获取');
    }
  },
  getPurchase: async (id) => {
    try {
      return await apiClient.get(`/api/purchases/${id}`);
    } catch (error) {
      throw handleApiError(error, '采购', '获取');
    }
  },
  createPurchase: async (purchaseData) => {
    try {
      return await apiClient.post('/api/purchases', purchaseData);
    } catch (error) {
      throw handleApiError(error, '采购', '创建');
    }
  },
  updatePurchase: async (id, purchaseData) => {
    try {
      return await apiClient.patch(`/api/purchases/${id}`, purchaseData);
    } catch (error) {
      throw handleApiError(error, '采购', '更新');
    }
  },
  deletePurchase: async (id) => {
    try {
      return await apiClient.delete(`/api/purchases/${id}`);
    } catch (error) {
      throw handleApiError(error, '采购', '删除');
    }
  }
};

// 库存相关API
export const inventoryService = {
  getInventory: async (params) => {
    try {
      const response = await apiClient.get('/api/inventory', { params });
      return processResponse(response, 'inventory');
    } catch (error) {
      throw handleApiError(error, '库存列表', '获取');
    }
  },
  getInventoryItem: async (id) => {
    try {
      return await apiClient.get(`/api/inventory/${id}`);
    } catch (error) {
      throw handleApiError(error, '库存项', '获取');
    }
  },
  createInventoryItem: async (inventoryData) => {
    try {
      return await apiClient.post('/api/inventory', inventoryData);
    } catch (error) {
      throw handleApiError(error, '库存项', '创建');
    }
  },
  updateInventoryItem: async (id, inventoryData) => {
    try {
      return await apiClient.patch(`/api/inventory/${id}`, inventoryData);
    } catch (error) {
      throw handleApiError(error, '库存项', '更新');
    }
  },
  deleteInventoryItem: async (id) => {
    try {
      return await apiClient.delete(`/api/inventory/${id}`);
    } catch (error) {
      throw handleApiError(error, '库存项', '删除');
    }
  },
  // 获取库存记录
  getInventoryTransactions: async (params) => {
    try {
      const response = await apiClient.get('/api/inventory/transactions', { params });
      return processPaginatedResponse(response, 'transactions', params);
    } catch (error) {
      throw handleApiError(error, '库存记录', '获取');
    }
  },
  // 获取特定产品的库存记录
  getItemTransactions: async (itemId, params) => {
    try {
      const response = await apiClient.get(`/api/inventory/${itemId}/transactions`, { params });
      return processPaginatedResponse(response, 'transactions', params);
    } catch (error) {
      throw handleApiError(error, '产品库存记录', '获取');
    }
  },
  // 入库操作
  createStockIn: async (stockInData) => {
    try {
      return await apiClient.post('/api/inventory/stock-in', stockInData);
    } catch (error) {
      throw handleApiError(error, '入库操作', '执行');
    }
  },
  // 出库操作
  createStockOut: async (stockOutData) => {
    try {
      return await apiClient.post('/api/inventory/stock-out', stockOutData);
    } catch (error) {
      throw handleApiError(error, '出库操作', '执行');
    }
  },
  // 库存调整
  createStockAdjustment: async (adjustmentData) => {
    try {
      return await apiClient.post('/api/inventory/adjustments', adjustmentData);
    } catch (error) {
      throw handleApiError(error, '库存调整', '执行');
    }
  },
  // 获取库存警报
  getStockAlerts: async () => {
    try {
      return await apiClient.get('/api/inventory/alerts');
    } catch (error) {
      throw handleApiError(error, '库存警报', '获取');
    }
  },
  // 导出库存记录
  exportTransactions: async (params) => {
    try {
      return await apiClient.get('/api/inventory/transactions/export', { 
        params,
        responseType: 'blob'  // 指定响应类型为二进制数据
      });
    } catch (error) {
      throw handleApiError(error, '库存记录', '导出');
    }
  }
};

// 客户相关API
export const clientService = {
  getClients: getClients,
  getClient: getClientById,
  createClient: createClient,
  updateClient: updateClient,
  deleteClient: deleteClient
};

// 报销相关API
export const reimbursementService = {
  getReimbursements: async (params) => {
    try {
      const response = await apiClient.get('/api/reimbursements', { params });
      return processPaginatedResponse(response, 'reimbursements', params);
    } catch (error) {
      throw handleApiError(error, '报销列表', '获取');
    }
  },
  getReimbursement: async (id) => {
    try {
      return await apiClient.get(`/api/reimbursements/${id}`);
    } catch (error) {
      throw handleApiError(error, '报销', '获取');
    }
  },
  createReimbursement: async (formData) => {
    try {
      return await apiClient.post('/api/reimbursements', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    } catch (error) {
      throw handleApiError(error, '报销', '创建');
    }
  },
  updateReimbursement: async (id, formData) => {
    try {
      return await apiClient.put(`/api/reimbursements/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    } catch (error) {
      throw handleApiError(error, '报销', '更新');
    }
  },
  deleteReimbursement: async (id) => {
    try {
      return await apiClient.delete(`/api/reimbursements/${id}`);
    } catch (error) {
      throw handleApiError(error, '报销', '删除');
    }
  },
  getProjectOptions: async () => {
    try {
      return await apiClient.get('/api/reimbursements/project-options', { noCache: true });
    } catch (error) {
      throw handleApiError(error, '项目选项', '获取');
    }
  },
  getSupplierOptions: async () => {
    try {
      return await apiClient.get('/api/reimbursements/supplier-options', { noCache: true });
    } catch (error) {
      throw handleApiError(error, '供应商选项', '获取');
    }
  },
  getUserOptions: async () => {
    try {
      return await apiClient.get('/api/reimbursements/user-options');
    } catch (error) {
      throw handleApiError(error, '用户选项', '获取');
    }
  }
};

// 分包相关API
export const subcontractService = {
  getSubcontracts: async (params) => {
    try {
      const response = await apiClient.get('/api/subcontracts', { params });
      return processPaginatedResponse(response, 'subcontracts', params);
    } catch (error) {
      throw handleApiError(error, '分包列表', '获取');
    }
  },
  getSubcontract: async (id) => {
    try {
      return await apiClient.get(`/api/subcontracts/${id}`);
    } catch (error) {
      throw handleApiError(error, '分包', '获取');
    }
  },
  createSubcontract: async (data) => {
    try {
      return await apiClient.post('/api/subcontracts', data);
    } catch (error) {
      throw handleApiError(error, '分包', '创建');
    }
  },
  updateSubcontract: async (id, data) => {
    try {
      return await apiClient.put(`/api/subcontracts/${id}`, data);
    } catch (error) {
      throw handleApiError(error, '分包', '更新');
    }
  },
  deleteSubcontract: async (id) => {
    try {
      return await apiClient.delete(`/api/subcontracts/${id}`);
    } catch (error) {
      throw handleApiError(error, '分包', '删除');
    }
  }
};

// 工时相关API
export const workhoursService = {
  getWorkHours: async (params) => {
    try {
      const response = await apiClient.get('/api/workhours', { params });
      return processResponse(response, 'workhours');
    } catch (error) {
      throw handleApiError(error, '工时列表', '获取');
    }
  },
  getWorkHour: async (id) => {
    try {
      return await apiClient.get(`/api/workhours/${id}`);
    } catch (error) {
      throw handleApiError(error, '工时', '获取');
    }
  },
  createWorkHour: async (data) => {
    try {
      return await apiClient.post('/api/workhours', data);
    } catch (error) {
      throw handleApiError(error, '工时', '创建');
    }
  },
  updateWorkHour: async (id, data) => {
    try {
      return await apiClient.put(`/api/workhours/${id}`, data);
    } catch (error) {
      throw handleApiError(error, '工时', '更新');
    }
  },
  deleteWorkHour: async (id) => {
    try {
      return await apiClient.delete(`/api/workhours/${id}`);
    } catch (error) {
      throw handleApiError(error, '工时', '删除');
    }
  },
  getWorkHoursStatistics: async (params) => {
    try {
      return await apiClient.get('/api/workhours/statistics', { params });
    } catch (error) {
      throw handleApiError(error, '工时统计', '获取');
    }
  },
  getWorkHoursCalendar: async (params) => {
    try {
      return await apiClient.get('/api/workhours/calendar', { params });
    } catch (error) {
      throw handleApiError(error, '工时日历', '获取');
    }
  }
};

// 员工相关API
export const employeeService = {
  getAllEmployees: getAllEmployees,
  getContractWorkers: getContractWorkers,
  getTemporaryWorkers: getTemporaryWorkers,
  deleteContractWorker: deleteContractWorker,
  deleteTemporaryWorker: deleteTemporaryWorker
};

// 订单相关API
export const orderService = {
  getOrders: getOrders,
  getOrdersPaginated: getOrdersPaginated,
  getOrder: getOrderById,
  createOrder: createOrder,
  updateOrder: updateOrder,
  deleteOrder: deleteOrder
};

// 产品相关API
export const productService = {
  getProducts: getProducts,
  getProductsPaginated: getProductsPaginated,
  getProduct: getProductById,
  createProduct: createProduct,
  updateProduct: updateProduct,
  deleteProduct: deleteProduct
};

// 财务相关API
export const financeService = {
  getFinanceStats: getFinanceStats,
  getPublicFinanceStats: getPublicFinanceStats
};

// 建设单位相关API
export const constructionService = {
};

// 更新用户服务，添加获取下拉列表功能
userService.getUsersForDropdown = getUsersForDropdown;

// 默认导出所有API服务
export default {
  // 用户相关API
  users: userService,

  // 产品相关API
  getProducts,
  getProductsPaginated,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,

  // 项目相关API
  getProjects,
  getPublicProjects,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,

  // 供应商相关API
  getSuppliers,
  getPublicSuppliers,
  deleteSupplier: async (id) => {
    try {
      return await apiClient.delete(`/api/suppliers/${id}`);
    } catch (error) {
      throw handleApiError(error, '供应商', '删除');
    }
  },

  // 客户相关API
  getClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient,

  // 财务相关API
  getFinanceStats,
  getPublicFinanceStats,

  // 订单相关API
  getOrders,
  getOrdersPaginated,
  getOrderById,
  createOrder,
  updateOrder,
  deleteOrder,

  // 员工相关API
  getAllEmployees,
  getContractWorkers,
  getTemporaryWorkers,
  deleteContractWorker,
  deleteTemporaryWorker,

  // 建设单位相关API
  getConstructionUnits,

  // 命名服务导出
  auth: authService,
  users: userService,
  categories: categoryService,
  documents: documentService,
  tags: tagService,
  comments: commentService,
  attachments: attachmentService,
  projects: projectService,
  suppliers: supplierService,
  purchases: purchaseService,
  inventory: inventoryService,
  clients: clientService,
  reimbursements: reimbursementService,
  subcontracts: subcontractService,
  workhours: workhoursService,
  employees: employeeService,
  orders: orderService,
  products: productService,
  finance: financeService,
  construction: constructionService
};