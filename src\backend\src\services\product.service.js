const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Product, User, sequelize } = require('../models');

/**
 * Create a product
 * @param {Object} productBody
 * @returns {Promise<Product>}
 */
const createProduct = async (productBody) => {
  // Check if product with same code already exists
  if (productBody.code) {
    const existingProduct = await Product.findOne({ where: { code: productBody.code } });
    if (existingProduct) {
      throw new ApiError(400, 'Product with this code already exists');
    }
  }

  return Product.create(productBody);
};

/**
 * Get product by id
 * @param {string} id
 * @returns {Promise<Product>}
 */
const getProductById = async (id) => {
  const product = await Product.findByPk(id, {
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!product) {
    throw new ApiError(404, 'Product not found');
  }
  return product;
};

/**
 * Update product by id
 * @param {string} productId
 * @param {Object} updateBody
 * @returns {Promise<Product>}
 */
const updateProductById = async (productId, updateBody) => {
  const product = await getProductById(productId);

  // Check if product code is being updated and already exists
  if (updateBody.code && updateBody.code !== product.code) {
    const existingProduct = await Product.findOne({
      where: { code: updateBody.code }
    });
    if (existingProduct) {
      throw new ApiError(400, 'Product with this code already exists');
    }
  }

  Object.assign(product, updateBody);
  await product.save();
  return product;
};

/**
 * Delete product by id
 * @param {string} productId
 * @returns {Promise<void>}
 */
const deleteProductById = async (productId) => {
  const product = await getProductById(productId);
  await product.destroy();
};

/**
 * Query for products
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Products and pagination info
 */
const queryProducts = async (filter, options) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};

  if (filter.name) {
    whereClause.name = { [Op.like]: `%${filter.name}%` };
  }

  if (filter.code) {
    whereClause.code = { [Op.like]: `%${filter.code}%` };
  }

  if (filter.category) {
    whereClause.category = filter.category;
  }

  if (filter.status) {
    // 处理中文库存状态筛选
    if (filter.status === '库存充足') {
      // 库存充足的条件是: stock > minStock
      whereClause[Op.and] = whereClause[Op.and] || [];
      whereClause[Op.and].push({
        stock: { [Op.gt]: sequelize.col('minStock') }
      });
    } else if (filter.status === '库存预警') {
      // 库存预警的条件是: stock = minStock
      whereClause[Op.and] = whereClause[Op.and] || [];
      whereClause[Op.and].push({
        stock: { [Op.eq]: sequelize.col('minStock') }
      });
    } else if (filter.status === '库存不足') {
      // 库存不足的条件是: stock < minStock
      whereClause[Op.and] = whereClause[Op.and] || [];
      whereClause[Op.and].push({
        stock: { [Op.lt]: sequelize.col('minStock') }
      });
    } else {
      // 如果不是中文状态描述，则按原状态字段筛选
      whereClause.status = filter.status;
    }
  }

  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${filter.search}%` } },
      { code: { [Op.like]: `%${filter.search}%` } },
      { description: { [Op.like]: `%${filter.search}%` } }
    ];
  }

  if (filter.minPrice) {
    whereClause.price = {
      ...whereClause.price,
      [Op.gte]: filter.minPrice
    };
  }

  if (filter.maxPrice) {
    whereClause.price = {
      ...whereClause.price,
      [Op.lte]: filter.maxPrice
    };
  }

  if (filter.tags) {
    // Handle tags filtering - this is a bit complex since tags are stored as JSON
    // This is a simplified approach that checks if any of the requested tags exist in the product's tags
    const tagsArray = Array.isArray(filter.tags) ? filter.tags : [filter.tags];

    // Create a condition for each tag
    const tagConditions = tagsArray.map(tag => ({
      tags: { [Op.like]: `%${tag}%` }
    }));

    // Add the tag conditions to the where clause
    whereClause[Op.or] = [...(whereClause[Op.or] || []), ...tagConditions];
  }

  // Query with pagination
  const { count, rows } = await Product.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

/**
 * Get inventory overview
 * @returns {Promise<Object>} Inventory statistics
 */
const getInventoryOverview = async () => {
  const stats = await Product.findAll({
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('id')), 'totalProducts'],
      [sequelize.fn('SUM', sequelize.col('stock')), 'totalStock'],
      [sequelize.fn('SUM', sequelize.literal('stock * price')), 'totalValue']
    ]
  });

  const lowStockCount = await Product.count({
    where: sequelize.literal('stock <= minStock')
  });

  const categoryStats = await Product.findAll({
    attributes: [
      'category',
      [sequelize.fn('COUNT', sequelize.col('id')), 'productCount'],
      [sequelize.fn('SUM', sequelize.col('stock')), 'totalStock'],
      [sequelize.fn('SUM', sequelize.literal('stock * price')), 'totalValue']
    ],
    group: ['category']
  });

  return {
    summary: stats[0],
    lowStockCount,
    categoryBreakdown: categoryStats
  };
};

/**
 * Get stock alerts
 * @returns {Promise<Array>} List of products with low stock
 */
const getStockAlerts = async () => {
  return Product.findAll({
    where: sequelize.literal('stock <= minStock'),
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });
};

/**
 * Batch update stock levels
 * @param {Array} updates Array of {productId, newStock} objects
 * @returns {Promise<Array>} Updated products
 */
const batchUpdateStock = async (updates) => {
  const result = await sequelize.transaction(async (t) => {
    const updatePromises = updates.map(async ({ productId, newStock }) => {
      const product = await Product.findByPk(productId, { transaction: t });
      if (!product) {
        throw new ApiError(404, `Product with id ${productId} not found`);
      }
      product.stock = newStock;
      return product.save({ transaction: t });
    });
    return Promise.all(updatePromises);
  });
  return result;
};

/**
 * Get stock level report
 * @param {Object} filter - Filter options
 * @returns {Promise<Object>} Stock level report
 */
const getStockLevelReport = async (filter) => {
  const whereClause = {};
  if (filter.category) {
    whereClause.category = filter.category;
  }
  if (filter.minStock) {
    whereClause.stock = {
      ...whereClause.stock,
      [Op.gte]: filter.minStock
    };
  }
  if (filter.maxStock) {
    whereClause.stock = {
      ...whereClause.stock,
      [Op.lte]: filter.maxStock
    };
  }

  const products = await Product.findAll({
    where: whereClause,
    attributes: [
      'id',
      'name',
      'code',
      'category',
      'stock',
      'minStock',
      'price',
      [sequelize.literal('stock * price'), 'value']
    ],
    order: [['stock', 'ASC']]
  });

  const summary = {
    totalProducts: products.length,
    totalStock: products.reduce((sum, p) => sum + p.stock, 0),
    totalValue: products.reduce((sum, p) => sum + (p.stock * p.price), 0),
    averageStock: products.reduce((sum, p) => sum + p.stock, 0) / products.length,
    lowStockCount: products.filter(p => p.stock <= p.minStock).length
  };

  return {
    summary,
    products
  };
};

module.exports = {
  createProduct,
  getProductById,
  updateProductById,
  deleteProductById,
  queryProducts,
  getInventoryOverview,
  getStockAlerts,
  batchUpdateStock,
  getStockLevelReport,
};